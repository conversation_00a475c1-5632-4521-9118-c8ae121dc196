<?php
// migrate_profile_pictures.php
// Run this script once to add profile picture support to the database

require_once 'config/database.php';

// Check if profile_picture column exists in tblusers table
$check_column_sql = "SHOW COLUMNS FROM tblusers LIKE 'profile_picture'";
$result = mysqli_query($conn, $check_column_sql);

if (mysqli_num_rows($result) == 0) {
    // Add profile_picture column
    $add_column_sql = "ALTER TABLE tblusers ADD COLUMN profile_picture VARCHAR(255) NULL AFTER email";
    if (mysqli_query($conn, $add_column_sql)) {
        echo "✅ Added profile_picture column to tblusers table.\n";
    } else {
        echo "❌ Error adding profile_picture column: " . mysqli_error($conn) . "\n";
    }
} else {
    echo "✅ profile_picture column already exists in tblusers table.\n";
}

// Create uploads directory structure
$upload_dirs = [
    'uploads',
    'uploads/profile_pictures'
];

foreach ($upload_dirs as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✅ Created directory: $dir\n";
        } else {
            echo "❌ Failed to create directory: $dir\n";
        }
    } else {
        echo "✅ Directory already exists: $dir\n";
    }
}

// Create .htaccess file for uploads directory security
$htaccess_content = "# Prevent direct access to uploaded files
Options -Indexes
<Files *.php>
    Deny from all
</Files>
<Files *.phtml>
    Deny from all
</Files>
<Files *.php3>
    Deny from all
</Files>
<Files *.php4>
    Deny from all
</Files>
<Files *.php5>
    Deny from all
</Files>
<Files *.pl>
    Deny from all
</Files>
<Files *.py>
    Deny from all
</Files>
<Files *.jsp>
    Deny from all
</Files>
<Files *.asp>
    Deny from all
</Files>
<Files *.sh>
    Deny from all
</Files>
<Files *.cgi>
    Deny from all
</Files>";

$htaccess_path = 'uploads/.htaccess';
if (!file_exists($htaccess_path)) {
    if (file_put_contents($htaccess_path, $htaccess_content)) {
        echo "✅ Created security .htaccess file in uploads directory.\n";
    } else {
        echo "❌ Failed to create .htaccess file.\n";
    }
} else {
    echo "✅ Security .htaccess file already exists.\n";
}

echo "\n🎉 Profile picture migration completed!\n";
echo "You can now use the profile picture feature.\n";

mysqli_close($conn);
?>

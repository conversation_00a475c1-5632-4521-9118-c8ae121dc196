<?php
// credentials.php
ini_set('display_errors', 0); // Don't display errors directly to users
ini_set('log_errors', 1); // Log errors to the server's error log
error_reporting(E_ALL); // Report all errors for logging purposes

// --- Session and Authentication (IMPORTANT!) ---
session_start(); // Start the session
// Basic check if the user is logged in. Replace with your actual login check logic.
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php'); // Redirect to login page if not logged in
    exit;
}
// Optional: Role check (if you have roles) - Assuming 'admin' can manage users
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
     // Redirect or show an error message if the user doesn't have permission
     // Use die() only for critical errors or access denial, consider a user-friendly message page
     die('Access Denied: You do not have permission to manage credentials.');
}
// --- End Session and Authentication ---


require_once 'config/database.php'; // Ensure this path is correct

// Check database connection
$db_connection_error = null;
if (!$conn) {
    error_log("Database connection failed in credentials.php: " . mysqli_connect_error());
    $db_connection_error = "Error connecting to the database. Please try again later or contact support.";
    // Stop execution if DB connection fails critically
    // die($db_connection_error); // Or display a dedicated error page
} else {
    mysqli_set_charset($conn, "utf8mb4"); // Ensure UTF-8
}

// --- Pagination Configuration ---
$results_per_page_options = [5, 10, 20, 50, 100];
$default_results_per_page = 10;
$current_page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$results_per_page = isset($_GET['limit']) ? (int)$_GET['limit'] : $default_results_per_page;
if (!in_array($results_per_page, $results_per_page_options)) {
    $results_per_page = $default_results_per_page;
}

// --- Search Term (Optional - Add later if needed) ---
$search_term = isset($_GET['search']) ? trim($_GET['search']) : '';

// --- Fetch User Data ---
$users_result = false;
$users_fetch_error = null;
$total_users = 0;
$total_pages = 1;
$offset = 0;
$stmt_users = null; // Initialize statement variable

if (!$db_connection_error) {
    // --- Build WHERE clause (if search is added) ---
    $sql_where_clause = " WHERE 1 "; // Base clause
    $query_params = [];
    $query_param_types = '';

    // Example Search Logic (Uncomment and adapt if needed)
    /*
    if (!empty($search_term)) {
        $like_term = '%' . $search_term . '%';
        $searchable_columns = ['username', 'full_name', 'email', 'role']; // Adjust searchable columns
        $search_conditions = [];
        foreach ($searchable_columns as $column) {
            $search_conditions[] = "`" . $column . "` LIKE ?";
            $query_params[] = $like_term;
            $query_param_types .= 's';
        }
        $sql_where_clause .= " AND (" . implode(" OR ", $search_conditions) . ")";
    }
    */

    // --- Calculate Total Users ---
    $stmt_total = null;
    $total_users_query = "SELECT COUNT(*) as total FROM tblusers" . $sql_where_clause;
    $stmt_total = mysqli_prepare($conn, $total_users_query);

    if ($stmt_total) {
        $total_result = null;
        if (!empty($query_params)) {
            // Bind search params if they exist
             if (mysqli_stmt_bind_param($stmt_total, $query_param_types, ...$query_params)) {
                if (!mysqli_stmt_execute($stmt_total)) {
                    $users_fetch_error = "Error executing user count: " . mysqli_stmt_error($stmt_total);
                    error_log("Credentials User Count Execute Error: " . mysqli_stmt_error($stmt_total));
                } else {
                    $total_result = mysqli_stmt_get_result($stmt_total);
                }
             } else {
                $users_fetch_error = "Error binding params for user count: " . mysqli_stmt_error($stmt_total);
                error_log("Credentials User Count Bind Error: " . mysqli_stmt_error($stmt_total));
             }
        } else {
             if (!mysqli_stmt_execute($stmt_total)) {
                 $users_fetch_error = "Error executing user count: " . mysqli_stmt_error($stmt_total);
                 error_log("Credentials User Count Execute Error: " . mysqli_stmt_error($stmt_total));
             } else {
                 $total_result = mysqli_stmt_get_result($stmt_total);
             }
        }


        if ($users_fetch_error === null && $total_result) {
             $total_row = mysqli_fetch_assoc($total_result);
             $total_users = $total_row['total'] ?? 0;
             mysqli_free_result($total_result);
        } elseif ($users_fetch_error === null) {
             // This condition might occur if get_result returns false but execute was true
             $users_fetch_error = "Error reading user count result.";
             error_log("Credentials User Count Result Error: " . mysqli_error($conn)); // Use mysqli_error as stmt might be closed
        }
        mysqli_stmt_close($stmt_total);
    } else {
        $users_fetch_error = "Error preparing user count statement.";
        error_log("Credentials User Count Prepare Error: " . mysqli_error($conn));
    }

    // --- Fetch Paginated Users ---
    if ($users_fetch_error === null) {
        $total_pages = ($results_per_page > 0) ? ceil($total_users / $results_per_page) : 1;
        $total_pages = max(1, $total_pages); // Ensure at least 1 page
        if ($current_page > $total_pages && $total_pages > 0) $current_page = $total_pages;
        if ($current_page < 1) $current_page = 1;
        $offset = ($current_page - 1) * $results_per_page;

        if ($total_users > 0) {
            // Select columns needed for display - **NEVER SELECT PASSWORD HASH HERE**
            $users_sql = "SELECT `user_id`, `username`, `full_name`, `email`, `role`, `status`, `bureau`, `created_at`, `reset_password`
                          FROM `tblusers`" . $sql_where_clause .
                          " ORDER BY `username` ASC LIMIT ? OFFSET ?";

            // Combine search params with pagination params
            $all_bind_params = $query_params; // Start with search params
            $all_bind_params[] = $results_per_page; // Add limit
            $all_bind_params[] = $offset;         // Add offset
            $combined_param_types = $query_param_types . 'ii'; // Append types for limit and offset

            $stmt_users = mysqli_prepare($conn, $users_sql);
            if ($stmt_users) {
                 // Bind all parameters
                 if (!empty($all_bind_params)) {
                    if (!mysqli_stmt_bind_param($stmt_users, $combined_param_types, ...$all_bind_params)) {
                        $users_fetch_error = "Error binding parameters for user fetch: " . mysqli_stmt_error($stmt_users);
                        error_log("Credentials User Fetch Bind Error: " . mysqli_stmt_error($stmt_users));
                    }
                 }

                 // Execute only if binding was successful (or no params needed)
                 if ($users_fetch_error === null) {
                     if (!mysqli_stmt_execute($stmt_users)) {
                         $users_fetch_error = "Error fetching user data: " . mysqli_stmt_error($stmt_users);
                         error_log("Credentials User Fetch Execute Error: " . mysqli_stmt_error($stmt_users));
                     } else {
                         $users_result = mysqli_stmt_get_result($stmt_users); // Assign result object
                         if (!$users_result) {
                             $users_fetch_error = "Error getting result set for users: " . mysqli_error($conn);
                             error_log("Credentials User Fetch Result Error: " . mysqli_error($conn));
                         }
                     }
                 }
                 // Don't close statement here if using $users_result later in the loop
            } else {
                $users_fetch_error = "Error preparing to fetch users: " . mysqli_error($conn);
                error_log("Credentials User Fetch Prepare Error: " . mysqli_error($conn));
            }
        } elseif ($users_fetch_error === null) { // Only set this if no prior errors
             // No users found message (handle search term)
             $users_fetch_error = !empty($search_term) ? "No users found matching the search criteria." : "No users found in the database.";
             $total_pages = 1; // Ensure page 1 exists conceptually
        }
    }
} elseif ($db_connection_error) {
    // Set error message if DB connection failed initially
    $users_fetch_error = "Cannot fetch users due to database connection error.";
    $total_pages = 1;
    $total_users = 0;
}


// --- Build URL Parameters string for pagination links ---
$url_params_array = [];
if (!empty($search_term)) $url_params_array['search'] = $search_term;
if ($results_per_page !== $default_results_per_page) $url_params_array['limit'] = $results_per_page;
// http_build_query handles encoding
$url_params_for_pagination = !empty($url_params_array) ? '&' . http_build_query($url_params_array, '', '&', PHP_QUERY_RFC3986) : '';


// --- Helper Functions ---
function formatUserDateTime($date_string) {
    if (empty($date_string) || $date_string === '0000-00-00 00:00:00' || is_null($date_string)) return 'Never';
    try {
        $dt = new DateTime($date_string);
        return $dt->format('M d, Y H:i');
    } catch (Exception $e) {
        error_log("Invalid date format in formatUserDateTime: " . $date_string);
        return 'Invalid Date';
    }
}
function formatUserDate($date_string) {
    if (empty($date_string) || $date_string === '0000-00-00 00:00:00' || is_null($date_string)) return 'N/A';
     try {
        $dt = new DateTime($date_string);
        return $dt->format('M d, Y');
    } catch (Exception $e) {
        error_log("Invalid date format in formatUserDate: " . $date_string);
        return 'Invalid Date';
    }
}

$current_page_name = 'credentials'; // For sidebar active state

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Credentials Management - Activity Monitoring</title>
    <link rel="stylesheet" href="css/style.css"> <!-- Ensure this path is correct -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Add specific styles for credentials page if needed */
        .table-actions { white-space: nowrap; text-align: right; }
        .table-actions .btn { margin-left: 5px; margin-bottom: 5px; } /* Use margin-left instead of margin-right */
        .text-right { text-align: right; }

        /* Make action buttons more compact */
        .table-actions .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }
        .user-row { cursor: pointer; transition: background-color 0.2s ease; }
        .user-row:hover { background-color: rgba(106, 90, 224, 0.05); }
        .status-active { color: var(--green-color); font-weight: bold; }
        .status-inactive { color: var(--red-color); font-style: italic; }
        .role-admin { font-weight: bold; }
        .last-login-col { white-space: nowrap; }
        .db-error-message { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; padding: 15px; margin: 15px; border-radius: 4px; }
        .table-message.error { color: var(--red-color); background-color: rgba(255, 0, 0, 0.05); }
        .table-message { text-align: center; padding: 20px; color: var(--text-light); }
        .pagination-controls { display: flex; justify-content: space-between; align-items: center; padding: 15px 0; flex-wrap: wrap; gap: 10px; }
        .pagination-info { font-size: 0.9em; color: var(--text-light); }
        .pagination-nav { display: flex; gap: 5px; flex-wrap: wrap; } /* Allow wrapping for page numbers */

        /* Rows per page styles */
        .rows-per-page { display: flex; align-items: center; gap: 10px; }
        .rows-per-page label { font-size: 0.9em; color: var(--text-light); margin-bottom: 0; }
        .rows-per-page select { width: auto; padding: 6px 10px; border-radius: var(--border-radius); border: 1px solid var(--border-color); background-color: var(--input-bg); color: var(--text-color); font-size: 14px; }

        /* Table controls layout */
        .table-controls { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; flex-wrap: wrap; gap: 10px; }
        .table-left-controls, .table-right-controls { display: flex; align-items: center; gap: 10px; }

        /* Button styles to match the main application style */
        .btn {
            padding: 8px 15px;
            border-radius: var(--border-radius);
            font-size: 14px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
            cursor: pointer;
            border: 1px solid transparent;
            line-height: 1.5;
            text-decoration: none;
            white-space: nowrap;
        }
        .btn i { line-height: 1; }
        .btn:disabled, .btn.disabled { cursor: not-allowed !important; opacity: 0.6 !important; background-color: #ccc; border-color: #ccc; color:#666; } /* More prominent disabled style */
        .btn:focus { outline: none; box-shadow: 0 0 0 2px rgba(106, 90, 224, 0.3); }
        .btn-secondary { background-color: var(--bg-light); color: var(--secondary-color); border: 1px solid var(--border-color); }
        .btn-secondary:hover:not(:disabled) { background-color: var(--bg-color); }
        .btn-primary { background-color: var(--primary-color); color: white; border-color: var(--primary-color); }
        .btn-primary:hover:not(:disabled) { background-color: #5a4bd3; border-color: #5a4bd3; }
        .btn-warning { background-color: var(--yellow-color); color: white; border-color: var(--yellow-color); }
        .btn-warning:hover:not(:disabled) { background-color: #d4a017; border-color: #d4a017; }
        .btn-delete, .btn-danger { background-color: var(--red-color); color: white; border-color: var(--red-color); }
        .btn-delete:hover:not(:disabled), .btn-danger:hover:not(:disabled) { background-color: #d32f2f; border-color: #d32f2f; }

        .btn-nav { background-color: var(--bg-light); color: var(--secondary-color); border: 1px solid var(--border-color); }
        .btn-nav:hover:not(.disabled) { background-color: var(--bg-color); }
        .btn-page { background-color: var(--bg-light); color: var(--secondary-color); border: 1px solid var(--border-color); min-width: 35px; justify-content: center; }
        .btn-page:hover:not(.active):not(.disabled) { background-color: var(--bg-color); }
        .btn-page.active { background-color: var(--primary-color); color: white; border-color: var(--primary-color); font-weight: bold; }
        .btn-sm { padding: 6px 12px; font-size: 13px; }


        /* Additional styles for credentials page */

        /* --- Improved Modal Form Layout --- */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Responsive columns */
            gap: 15px 20px; /* Row and column gap */
            padding: 10px 0; /* Add some padding */
        }
        .form-grid .form-field {
            display: flex;
            flex-direction: column; /* Stack label and input */
            gap: 5px; /* Space between label and input */
        }
        .form-grid .form-field label {
            font-weight: 500; /* Make labels slightly bolder */
            font-size: 0.9em;
            color: var(--text-color);
        }
        .form-grid .form-control { /* Style inputs/selects */
            padding: 9px 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background-color: var(--input-bg); /* Use theme variables */
            color: var(--text-color);
            width: 100%; /* Ensure they fill the grid cell */
            box-sizing: border-box; /* Include padding/border in width */
            font-size: 14px;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }
        .form-grid .form-control:focus {
             border-color: var(--primary-color);
             box-shadow: 0 0 0 2px rgba(106, 90, 224, 0.2); /* Focus ring */
             outline: none;
        }
         .form-grid .form-control[disabled] {
             background-color: #eee; /* Indicate disabled field */
             cursor: not-allowed;
             opacity: 0.7;
         }
        .form-grid .required { color: var(--red-color); margin-left: 2px;}

        /* Span full width for specific elements */
        .form-grid .full-width,
        .form-grid .error-message,
        .form-grid .password-section {
            grid-column: 1 / -1; /* Span full width */
        }

        /* Ensure form grid has exactly 2 columns for the 50/50 layout */
        .form-grid {
            grid-template-columns: 1fr 1fr; /* Two equal columns */
        }

        .form-grid .error-message {
            color: var(--red-color);
            background-color: rgba(220, 53, 69, 0.1);
            padding: 10px 15px;
            border: 1px solid rgba(220, 53, 69, 0.2);
            border-radius: var(--border-radius);
            margin-top: 10px;
            font-size: 0.9em;
            display: none; /* Hide initially */
        }
        .form-grid .error-message.visible { display: block; } /* Class to show error */


        /* Specific adjustments for Edit Modal sections */
        .form-grid .password-section {
             margin-top: 15px;
             padding-top: 15px;
             border-top: 1px solid var(--border-color);
        }
        .form-grid .password-section h3 { margin-bottom: 5px; font-size: 1.1em;}
        .form-grid .password-section p { margin-bottom: 10px; color: var(--text-light); font-size: 0.9em; }

        /* Password Strength Indicator */
         .password-strength { font-size: 0.85em; margin-top: 5px; height: 1.3em; font-weight: bold;}
         .strength-weak { color: var(--red-color); }
         .strength-medium { color: var(--yellow-color); }
         .strength-strong { color: var(--green-color); }

        /* Style loading/error indicators in Edit/Profile modal */
        .loading-indicator, .error-indicator { display: none; /* Hidden by default */ align-items: center; justify-content: center; padding: 20px; min-height: 100px; font-size: 1.1em;}
        .loading-indicator.active, .error-indicator.active { display: flex; } /* Show when active */
        .loading-indicator i { margin-right: 10px; }
        .error-indicator { color: var(--red-color); }
        .error-indicator i { margin-right: 10px; }

        /* Delete Modal Specifics - Updated to match reference */
        .confirm-delete-modal {
            max-width: 450px;
        }
        .confirm-delete-modal .modal-header {
            border-bottom: 1px solid #e9ecef;
            padding: 15px 20px;
        }
        .confirm-delete-modal .modal-header h2 {
            font-size: 18px;
            font-weight: 500;
        }
        .confirm-delete-modal .modal-body {
            padding: 20px;
            background-color: #fff;
        }
        .confirm-delete-modal .modal-body p {
            margin-bottom: 15px;
            font-size: 15px;
            color: #333;
        }
        .confirm-delete-modal .modal-body strong {
            font-weight: 500;
        }
        .confirm-delete-modal .warning-message {
            color: #dc3545;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 15px;
            margin-bottom: 0;
        }
        .confirm-delete-modal .warning-message i {
            color: #dc3545;
            font-size: 14px;
        }
        .confirm-delete-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        .confirm-delete-footer .btn {
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 4px;
        }
        .confirm-delete-footer .btn-delete {
            background-color: #dc3545;
            color: white;
            border: none;
        }
        .confirm-delete-footer .btn-delete:hover {
            background-color: #c82333;
        }
        .confirm-delete-footer .btn-secondary {
            background-color: #f8f9fa;
            color: #333;
            border: 1px solid #ddd;
        }
        .confirm-delete-footer .btn-secondary:hover {
            background-color: #e9ecef;
        }
        #deleteUserModal .modal-body .error-message {
            color: var(--red-color);
            background-color: rgba(220, 53, 69, 0.1);
            padding: 8px 12px;
            border: 1px solid rgba(220, 53, 69, 0.2);
            border-radius: var(--border-radius);
            margin-top: 15px;
            font-size: 0.9em;
            display: none; /* Hide initially */
            text-align: left;
        }
        #deleteUserModal .modal-body .error-message.visible { display: block; }

    </style>
</head>
<body>
    <!-- Custom Notification Element -->
    <div id="notification" class="notification"> <p class="notification-message" id="notificationMessage"></p> </div>

    <div class="app-container">
        <?php
        // Current page name is already set at line 214: $current_page_name = 'credentials';

        // Include the sidebar
        include 'sidebar.php';
        ?>

        <!-- ========== App Main Content ========== -->
        <main class="app-main">
            <!-- *** Dashboard Header *** -->
            <div class="dashboard-header">
                <div class="office-header">
                    <div class="office-logo"> <img src="images/dict-logo.png" alt="DICT Logo"> </div>
                    <div class="office-info"> <h1>DICT Surigao del Norte Provincial Office</h1> <p>Ferdinand M. Ortiz St., Brgy. Washington, Surigao City</p> </div>
                </div>
            </div>
            <!-- *** END Dashboard Header *** -->

            <!-- *** Main Header *** -->
            <div class="main-header">
                 <h2></h2>
            </div>
            <!-- *** END Main Header *** -->

             <?php if ($db_connection_error): ?>
                 <div class="db-error-message"> <strong>Database Error:</strong> <?php echo htmlspecialchars($db_connection_error); ?> </div>
             <?php endif; ?>

            <!-- Credentials Table Section -->
            <section class="credentials-list card">
                 <div class="table-controls">
                    <!-- Rows Per Page Selector (Left) -->
                    <div class="table-left-controls">
                        <div class="rows-per-page">
                            <label for="rowsPerPage">Rows per page:</label>
                            <select id="rowsPerPage" class="form-control" onchange="changeRowsPerPage(this.value)">
                                <?php foreach ($results_per_page_options as $option): ?>
                                <option value="<?php echo $option; ?>" <?php echo ($results_per_page == $option) ? 'selected' : ''; ?>><?php echo $option; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Add Button (Right) -->
                    <div class="table-right-controls">
                         <button type="button" class="btn btn-primary" id="addUserBtn">
                             <i class="fas fa-plus"></i> Add New User
                         </button>
                    </div>
                 </div>

                 <style>
                    /* Additional styles for proper alignment */
                    .table-controls {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 15px;
                    }
                    .table-right-controls {
                        display: flex;
                        justify-content: flex-end;
                    }


                 </style>

                 <div class="table-container">
                     <table>
                         <thead>
                             <tr>
                                 <th class="col-rownum">#</th>
                                 <th>Username</th>
                                 <th>Full Name</th>
                                 <th>Email</th>
                                 <th>Role</th>
                                 <th>Bureau</th>
                                 <th>Status</th>
                                 <th>Reset Password</th>
                                 <th class="text-right">Actions</th>
                             </tr>
                         </thead>
                         <tbody id="userTableBody">
                             <?php if ($users_fetch_error && !$users_result): // Prioritize showing fetch errors ?>
                                 <tr>
                                     <td colspan="9" class="table-message <?php echo str_contains(strtolower($users_fetch_error), 'error') ? 'error' : ''; ?>">
                                        <?php if (str_contains(strtolower($users_fetch_error), 'error')): ?><i class="fas fa-exclamation-triangle"></i>
                                        <?php else: ?><i class="fas fa-info-circle"></i><?php endif; ?>
                                        <?php echo htmlspecialchars($users_fetch_error); ?>
                                     </td>
                                 </tr>
                            <?php elseif ($users_result && mysqli_num_rows($users_result) > 0): ?>
                                 <?php $user_row_index = 0; ?>
                                 <?php while ($user = mysqli_fetch_assoc($users_result)): ?>
                                     <?php $row_number = $offset + $user_row_index + 1; ?>
                                     <tr data-user-id="<?php echo htmlspecialchars($user['user_id']); ?>" data-username="<?php echo htmlspecialchars($user['username']); ?>" class="user-row">
                                         <td class="col-rownum"><?php echo $row_number; ?></td>
                                         <td><?php echo htmlspecialchars($user['username']); ?></td>
                                         <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                         <td><?php echo htmlspecialchars($user['email'] ?? 'N/A'); ?></td>
                                         <td class="<?php echo ($user['role'] === 'admin') ? 'role-admin' : ''; ?>"><?php echo htmlspecialchars(ucfirst($user['role'])); ?></td>
                                         <td><?php echo htmlspecialchars($user['bureau'] ?? 'N/A'); ?></td>
                                         <td class="<?php echo ($user['status'] === 'active') ? 'status-active' : 'status-inactive'; ?>">
                                             <?php echo htmlspecialchars(ucfirst($user['status'])); ?>
                                         </td>
                                         <td><?php echo htmlspecialchars($user['reset_password'] ?: ''); ?></td>
                                         <td class="table-actions">
                                             <button type="button" class="btn btn-sm btn-secondary edit-user-btn" data-user-id="<?php echo htmlspecialchars($user['user_id']); ?>" title="Edit User">
                                                 <i class="fas fa-edit"></i> Edit
                                             </button>
                                             <button type="button" class="btn btn-sm btn-danger delete-user-btn" data-user-id="<?php echo htmlspecialchars($user['user_id']); ?>" data-username="<?php echo htmlspecialchars($user['username']); ?>" title="Delete User">
                                                 <i class="fas fa-trash-alt"></i> Delete
                                             </button>
                                         </td>
                                     </tr>
                                     <?php $user_row_index++; ?>
                                 <?php endwhile; ?>
                                 <?php if ($users_result) mysqli_free_result($users_result); // Free result set after loop ?>
                             <?php else: // No users found (and no fetch error reported earlier) ?>
                                 <tr> <td colspan="9" class="table-message"> <i class="fas fa-info-circle"></i> No users found. </td> </tr>
                             <?php endif; ?>
                             <?php
                                // Close the statement AFTER the loop if $users_result was used
                                if ($stmt_users && is_object($stmt_users)) mysqli_stmt_close($stmt_users);
                             ?>
                         </tbody>
                     </table>
                 </div>

                 <!-- Pagination Controls -->
                 <div class="pagination-controls">
                    <div class="pagination-info">
                        <?php
                        if ($total_users > 0) {
                             $start_index = $offset + 1;
                             $end_index = min($offset + $results_per_page, $total_users);
                             $filter_text = !empty($search_term) ? ' (filtered)' : '';
                             echo "Showing " . $start_index . " - " . $end_index . " of " . number_format($total_users) . " users" . $filter_text;
                         } else {
                             $filter_text = !empty($search_term) ? ' (filtered)' : '';
                             echo "Showing 0 - 0 of 0 users" . $filter_text;
                         }
                        ?>
                    </div>
                    <div class="pagination-nav">
                         <?php
                         $page_link_base = "credentials.php?page=";
                         // Previous Button
                         if ($current_page > 1): ?>
                             <a href="<?php echo $page_link_base . ($current_page - 1) . $url_params_for_pagination; ?>" class="btn btn-nav btn-sm">Previous</a>
                         <?php else: ?>
                             <span class="btn btn-nav btn-sm disabled">Previous</span>
                         <?php endif; ?>

                         <?php // Page Number Links
                         if ($total_pages > 1) {
                            $max_links = 5; // Max number links shown
                            $start_link = max(1, $current_page - floor($max_links / 2));
                            $end_link = min($total_pages, $start_link + $max_links - 1);
                            // Adjust start link if end link reaches total pages prematurely
                            if ($end_link == $total_pages && $total_pages >= $max_links) {
                                $start_link = max(1, $total_pages - $max_links + 1);
                            }

                            if ($start_link > 1) { echo '<a href="'.$page_link_base.'1'.$url_params_for_pagination.'" class="btn btn-page btn-sm">1</a>'; if ($start_link > 2) echo '<span class="btn btn-page btn-sm disabled">...</span>'; }
                            for ($i = $start_link; $i <= $end_link; $i++): echo '<a href="'.$page_link_base.$i.$url_params_for_pagination.'" class="btn btn-page btn-sm '.($i == $current_page ? 'active' : '').'">'.$i.'</a>'; endfor;
                            if ($end_link < $total_pages) { if ($end_link < $total_pages - 1) echo '<span class="btn btn-page btn-sm disabled">...</span>'; echo '<a href="'.$page_link_base.$total_pages.$url_params_for_pagination.'" class="btn btn-page btn-sm">'.$total_pages.'</a>'; }
                         } elseif ($total_users > 0 && $total_pages == 1) {
                             // Show page 1 even if it's the only page
                             echo '<a href="'.$page_link_base.'1'.$url_params_for_pagination.'" class="btn btn-page btn-sm active">1</a>';
                         }
                         ?>

                         <?php // Next Button
                         if ($current_page < $total_pages): ?>
                             <a href="<?php echo $page_link_base . ($current_page + 1) . $url_params_for_pagination; ?>" class="btn btn-nav btn-sm">Next</a>
                         <?php else: ?>
                             <span class="btn btn-nav btn-sm disabled">Next</span>
                         <?php endif; ?>
                    </div>
                </div>
                 <!-- End Pagination Controls -->
            </section>
        </main>
    </div> <!-- End .app-container -->

    <!-- ========== Modals ========== -->

    <!-- Edit User Modal -->
    <div id="editUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-user-edit"></i> Edit User</h2>
                <button type="button" class="close-modal" data-modal-id="editUserModal">×</button>
            </div>
            <div class="modal-body">
                <div id="editUserLoadingIndicator" class="loading-indicator">
                    <i class="fas fa-spinner fa-spin"></i> Loading user data...
                </div>
                <div id="editUserErrorIndicator" class="error-indicator">
                    <i class="fas fa-exclamation-triangle"></i> <span>Could not load user data. Please try again.</span>
                </div>
                <form id="editUserForm" class="form-grid" style="display: none;">
                    <input type="hidden" id="edit_user_id" name="user_id">
                    <!-- Full Name field (full width) -->
                    <div class="form-field full-width">
                        <label for="edit_full_name">Full Name <span class="required">*</span></label>
                        <input type="text" id="edit_full_name" name="full_name" required class="form-control">
                    </div>

                    <!-- Username and Email in one row (50/50) -->
                    <div class="form-field">
                        <label for="edit_username">Username <span class="required">*</span></label>
                        <input type="text" id="edit_username" name="username" required class="form-control">
                    </div>
                    <div class="form-field">
                        <label for="edit_email">Email</label>
                        <input type="email" id="edit_email" name="email" class="form-control">
                    </div>

                    <!-- Role and Bureau in one row (50/50) -->
                    <div class="form-field">
                        <label for="edit_role">Role <span class="required">*</span></label>
                        <select id="edit_role" name="role" required class="form-control">
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <div class="form-field">
                        <label for="edit_bureau">Bureau <span class="required">*</span></label>
                        <select id="edit_bureau" name="bureau" required class="form-control">
                            <option value="">Select Bureau</option>
                            <option value="Cybersecurity">Cybersecurity</option>
                            <option value="eLGU BPLS">eLGU BPLS</option>
                            <option value="FreeWifi4All">FreeWifi4All</option>
                            <option value="GovNet">GovNet</option>
                            <option value="IIDB">IIDB</option>
                            <option value="ILCDB">ILCDB</option>
                            <option value="GECS">GECS</option>
                        </select>
                    </div>

                    <!-- Status and Reset Password Token in one row (50/50) -->
                    <div class="form-field">
                        <label for="edit_status">Status <span class="required">*</span></label>
                        <select id="edit_status" name="status" required class="form-control">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    <div class="form-field">
                        <label for="edit_reset_password">Reset Password Token</label>
                        <input type="text" id="edit_reset_password" name="reset_password" class="form-control">
                        <small class="form-text text-muted">Token for password reset functionality.</small>
                    </div>

                    <!-- Password Section -->
                    <div class="password-section">
                        <h3>Change Password</h3>
                        <p>Leave blank to keep the current password.</p>
                    </div>
                    <div class="form-field">
                        <label for="edit_password">New Password</label>
                        <input type="password" id="edit_password" name="password" class="form-control" pattern=".{8,}" title="Minimum 8 characters">
                        <div id="edit_password_strength" class="password-strength"></div>
                    </div>
                    <div class="form-field">
                        <label for="edit_confirm_password">Confirm New Password</label>
                        <input type="password" id="edit_confirm_password" name="confirm_password" class="form-control">
                    </div>
                    <!-- End Password Section -->

                    <div id="editUserError" class="error-message"></div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="editUserModal">Cancel</button>
                <button type="submit" form="editUserForm" id="saveEditUserButton" class="btn btn-primary" disabled><i class="fas fa-save"></i> Save Changes</button>
            </div>
        </div>
    </div>

    <!-- Delete User Modal -->
    <div id="deleteUserModal" class="modal small">
        <div class="modal-content confirm-delete-modal">
            <div class="modal-header">
                <h2>Confirm Deletion</h2>
                <button type="button" class="close-modal" data-modal-id="deleteUserModal">×</button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the selected user <strong id="deleteUserName"></strong>?</p>
                <p class="warning-message"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone.</p>
                <div id="deleteUserError" class="error-message"></div>
            </div>
            <div class="modal-footer confirm-delete-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="deleteUserModal">Cancel</button>
                <button type="button" id="confirmDeleteUserButton" class="btn btn-delete">Delete</button>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div id="addUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-user-plus"></i> Add New User</h2>
                <button type="button" class="close-modal" data-modal-id="addUserModal">×</button> <!-- Changed data-modal-id -->
            </div>
            <div class="modal-body">
                <form id="addUserForm" class="form-grid">
                    <!-- Full Name field (full width) -->
                    <div class="form-field full-width">
                        <label for="add_full_name">Full Name <span class="required">*</span></label>
                        <input type="text" id="add_full_name" name="full_name" required class="form-control">
                    </div>

                    <!-- Username and Email in one row (50/50) -->
                    <div class="form-field">
                        <label for="add_username">Username <span class="required">*</span></label>
                        <input type="text" id="add_username" name="username" required class="form-control">
                    </div>
                    <div class="form-field">
                        <label for="add_email">Email</label>
                        <input type="email" id="add_email" name="email" class="form-control">
                    </div>

                    <!-- Role and Bureau in one row (50/50) -->
                    <div class="form-field">
                        <label for="add_role">Role <span class="required">*</span></label>
                        <select id="add_role" name="role" required class="form-control">
                            <option value="user" selected>User</option>
                            <option value="admin">Admin</option>
                            <!-- Add other roles as needed -->
                        </select>
                    </div>
                    <div class="form-field">
                        <label for="add_bureau">Bureau <span class="required">*</span></label>
                        <select id="add_bureau" name="bureau" required class="form-control">
                            <option value="" selected>Select Bureau</option>
                            <option value="Cybersecurity">Cybersecurity</option>
                            <option value="eLGU BPLS">eLGU BPLS</option>
                            <option value="FreeWifi4All">FreeWifi4All</option>
                            <option value="GovNet">GovNet</option>
                            <option value="IIDB">IIDB</option>
                            <option value="ILCDB">ILCDB</option>
                            <option value="GECS">GECS</option>
                        </select>
                    </div>

                    <!-- Status and Reset Password Token in one row (50/50) -->
                    <div class="form-field">
                        <label for="add_status">Status <span class="required">*</span></label>
                        <select id="add_status" name="status" required class="form-control">
                            <option value="active" selected>Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    <div class="form-field">
                        <label for="add_reset_password">Reset Password Token</label>
                        <input type="text" id="add_reset_password" name="reset_password" class="form-control">
                        <small class="form-text text-muted">Optional token for password reset functionality.</small>
                    </div>

                    <!-- Password fields (full width) -->
                    <div class="form-field full-width">
                        <label for="add_password">Password <span class="required">*</span></label>
                        <input type="password" id="add_password" name="password" required class="form-control" pattern=".{8,}" title="Minimum 8 characters">
                        <div id="add_password_strength" class="password-strength"></div>
                    </div>
                    <div class="form-field full-width">
                        <label for="add_confirm_password">Confirm Password <span class="required">*</span></label>
                        <input type="password" id="add_confirm_password" name="confirm_password" required class="form-control">
                    </div>
                    <div id="addUserError" class="error-message"></div> <!-- Error message div spans full width due to CSS -->
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="addUserModal">Cancel</button> <!-- Changed data-modal-id -->
                <button type="submit" form="addUserForm" id="saveAddUserButton" class="btn btn-primary"><i class="fas fa-save"></i> Add User</button>
            </div>
        </div>
    </div>



    <!-- Update Profile and Logout Modals are now included from modals.php -->
    <!-- ========== End Modals ========== -->

    <!-- AJAX Endpoint URL -->
    <script> const ajaxHandlerUrl = 'ajax_handler.php'; </script>

    <!-- Core JavaScript -->
    <script>
      document.addEventListener('DOMContentLoaded', () => {
        console.log("Credentials page loaded.");

        // --- Global Elements & Variables ---
        const notificationElement = document.getElementById('notification');
        const notificationMessageElement = document.getElementById('notificationMessage');
        const userTableBody = document.getElementById('userTableBody');
        let notificationTimeout;

        // Function to handle rows per page change
        window.changeRowsPerPage = function(value) {
            // Build the URL with the new limit parameter
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('limit', value);
            // Keep the current page if it exists, otherwise start at page 1
            if (!currentUrl.searchParams.has('page')) {
                currentUrl.searchParams.set('page', '1');
            }
            // Navigate to the new URL
            window.location.href = currentUrl.toString();
        };

        // Store modal elements by their ID for easy access
        const modals = {
            addUserModal: document.getElementById('addUserModal'),
            editUserModal: document.getElementById('editUserModal'),
            deleteUserModal: document.getElementById('deleteUserModal'),
            updateProfileModal: document.getElementById('updateProfileModal'),
            logoutConfirmModal: document.getElementById('logoutConfirmModal')
        };

        // --- Utility Functions ---
        function showNotification(message, type = 'success', duration = 3500) { // Slightly longer duration
            if (!notificationElement || !notificationMessageElement) return;
            clearTimeout(notificationTimeout);
            notificationMessageElement.textContent = message;
            notificationElement.className = 'notification ' + type; // Reset classes
            requestAnimationFrame(() => {
                 notificationElement.classList.add('visible');
            });
            notificationTimeout = setTimeout(() => {
                notificationElement.classList.remove('visible');
            }, duration);
        }

        async function performAjax(action, data = {}) {
            const formData = new FormData();
            formData.append('action', action);
            for (const key in data) {
                // Use hasOwnProperty to avoid iterating over prototype properties
                if (Object.prototype.hasOwnProperty.call(data, key)) {
                    const value = data[key];
                    // Append null/undefined values if necessary for backend logic, or skip
                    if (value !== null && value !== undefined) {
                         // Handle file uploads if necessary (check if value is File object)
                         if (value instanceof FileList) {
                            for (let i = 0; i < value.length; i++) {
                                formData.append(`${key}[]`, value[i]); // Append as array
                            }
                         } else if (typeof value === 'object' && !(value instanceof File)) {
                            // Optionally handle complex objects (e.g., JSON stringify) if needed
                             // formData.append(key, JSON.stringify(value));
                             console.warn(`Skipping non-File object for key ${key} in performAjax.`);
                         }
                         else {
                             formData.append(key, value);
                         }
                    } else {
                        // Decide if you need to send empty strings or skip nulls
                         // formData.append(key, ''); // Send empty string for null/undefined
                    }
                }
            }

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

            try {
                const response = await fetch(ajaxHandlerUrl, {
                    method: 'POST',
                    body: formData,
                    signal: controller.signal // Add abort signal
                });
                clearTimeout(timeoutId); // Clear timeout if fetch completes

                const responseClone = response.clone(); // Clone before reading body

                if (!response.ok) {
                    let errorMsg = `HTTP error! Status: ${response.status}`;
                    try {
                        const errorData = await response.json();
                        errorMsg = errorData.message || errorMsg; // Use server message if available
                    } catch (e) {
                         const errorText = await responseClone.text(); // Read text from clone
                         console.error("Non-JSON error response:", errorText);
                         errorMsg = `Server returned non-JSON response (Status: ${response.status}). Check console/logs.`;
                    }
                    throw new Error(errorMsg);
                }

                // Attempt to parse JSON, handle failure gracefully
                let responseData;
                try {
                    responseData = await response.json();
                } catch (jsonError) {
                    const errorText = await responseClone.text(); // Read text from clone
                    console.error('Non-JSON success response (check backend):', errorText);
                    throw new Error(`Server returned non-JSON response despite OK status. Check console/logs.`);
                }
                return responseData; // Should have { success: boolean, message: string, data: any }

            } catch (error) {
                 clearTimeout(timeoutId); // Clear timeout on error too
                 console.error('AJAX Error:', error);
                 let userMessage = 'An unexpected error occurred.';
                 if (error.name === 'AbortError') {
                    userMessage = 'The request timed out. Please try again.';
                 } else if (error.message.includes('non-JSON')) {
                    userMessage = 'An error occurred communicating with the server. Check console/logs.';
                } else if (error.message.includes('HTTP error')) {
                    userMessage = `A server error occurred. ${error.message}`; // Include specific error
                } else {
                    userMessage = `Error: ${error.message}.`;
                }
                // Avoid showing notification for timed-out requests if desired
                if (error.name !== 'AbortError') {
                    showNotification(userMessage, 'error', 5000);
                }
                // Return a standard error structure
                return { success: false, message: userMessage, data: null };
            }
        }


        function openModal(modalId) {
            const modal = modals[modalId];
            if (!modal) { console.error("Modal not found:", modalId); return; }
            resetModalContent(modalId); // Reset before opening
            modal.classList.add('visible');
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }

        function closeModal(modalId) {
            const modal = modals[modalId];
            if (modal) { modal.classList.remove('visible'); }
             // Only reset body overflow if *no* modals are visible
             const anyVisible = Object.values(modals).some(m => m?.classList.contains('visible'));
             if (!anyVisible) {
                 document.body.style.overflow = '';
             }
        }

        // Helper to show/hide modal errors
        function displayModalError(modalId, message) {
            const modal = modals[modalId];
            if (!modal) return;
            const errorDiv = modal.querySelector('.error-message');
            if (errorDiv) {
                errorDiv.textContent = message;
                errorDiv.classList.toggle('visible', !!message); // Show if message exists, hide if empty
            }
        }

        function resetModalContent(modalId) {
            const modal = modals[modalId];
            if (!modal) return;

             // Generic error reset & re-enable submit button
            displayModalError(modalId, ''); // Hide error message
            const submitButton = modal.querySelector('button[type="submit"], #confirmDeleteUserButton, #confirmLogoutButton');
            if (submitButton) {
                submitButton.disabled = false;
                // Reset button text if needed (find original text or use a default)
                 if (submitButton.id === 'saveAddUserButton') submitButton.innerHTML = '<i class="fas fa-save"></i> Add User';
                 else if (submitButton.id === 'saveEditUserButton') submitButton.innerHTML = '<i class="fas fa-save"></i> Save Changes';
                 else if (submitButton.id === 'saveProfileButton') submitButton.innerHTML = '<i class="fas fa-save"></i> Save Changes';
                 else if (submitButton.id === 'confirmLogoutButton') submitButton.innerHTML = 'Log Out';
                 else if (submitButton.id === 'confirmDeleteUserButton') submitButton.innerHTML = 'Delete';
            }

             // Specific resets based on modal ID
             switch (modalId) {
                 case 'addUserModal':
                     modal.querySelector('#addUserForm')?.reset();
                     resetPasswordStrength('add_password_strength');
                     break;
                 case 'editUserModal':
                     modal.querySelector('#editUserForm')?.reset();
                     modal.querySelector('#editUserLoadingIndicator').classList.remove('active');
                     modal.querySelector('#editUserErrorIndicator').classList.remove('active');
                     modal.querySelector('#editUserForm').style.display = 'none'; // Hide form
                     const saveEditBtn = modal.querySelector('#saveEditUserButton');
                     if (saveEditBtn) saveEditBtn.disabled = true; // Disable save until data loaded
                     resetPasswordStrength('edit_password_strength');
                     break;
                 case 'deleteUserModal':
                     document.getElementById('deleteUserName').textContent = '';
                     document.getElementById('confirmDeleteUserButton').removeAttribute('data-user-id');
                     break;
                 case 'updateProfileModal':
                     modal.querySelector('#updateProfileForm')?.reset();
                     modal.querySelector('#updateProfileLoadingIndicator').classList.remove('active');
                     modal.querySelector('#updateProfileErrorIndicator').classList.remove('active');
                     modal.querySelector('#updateProfileForm').style.display = 'none'; // Hide form
                     const saveProfileBtn = modal.querySelector('#saveProfileButton');
                     if (saveProfileBtn) saveProfileBtn.disabled = true; // Disable save until data loaded
                     resetPasswordStrength('profile_password_strength');
                     break;
                 case 'logoutConfirmModal':
                     // No specific form reset needed
                     break;
             }
        }


        // --- Password Strength Checker ---
        function checkPasswordStrength(password) {
            let strength = 0;
            if (!password) return 0;
            if (password.length >= 8) strength++;      // Length
            if (password.length >= 12) strength++;     // Longer length bonus
            if (password.match(/[a-z]/)) strength++;   // Lowercase
            if (password.match(/[A-Z]/)) strength++;   // Uppercase
            if (password.match(/[0-9]/)) strength++;   // Numbers
            if (password.match(/[\W_]/)) strength++;   // Symbols (includes underscore)
            return strength;
        }

        function displayPasswordStrength(password, strengthElementId) {
            const strengthElement = document.getElementById(strengthElementId);
            if (!strengthElement) return;

            const strength = checkPasswordStrength(password);
            let message = "";
            let cssClass = "";

            if (password.length === 0) {
                 message = ""; cssClass = "";
            } else if (strength < 3) { // Weak threshold
                message = "Weak"; cssClass = "strength-weak";
            } else if (strength < 5) { // Medium threshold
                message = "Medium"; cssClass = "strength-medium";
            } else { // Strong
                message = "Strong"; cssClass = "strength-strong";
            }
            strengthElement.textContent = message;
            // Set class ensuring only one strength class is active
            strengthElement.className = `password-strength ${cssClass}`;
        }

         function resetPasswordStrength(strengthElementId) {
            const strengthElement = document.getElementById(strengthElementId);
            if (strengthElement) {
                strengthElement.textContent = "";
                strengthElement.className = "password-strength";
            }
        }

        // Add listeners for password fields in all relevant modals
        document.getElementById('add_password')?.addEventListener('input', (e) => displayPasswordStrength(e.target.value, 'add_password_strength'));
        document.getElementById('profile_password')?.addEventListener('input', (e) => displayPasswordStrength(e.target.value, 'profile_password_strength'));


        // --- Modal Event Listeners ---
        Object.keys(modals).forEach(modalId => {
            const modal = modals[modalId];
            if (modal) {
                // Close buttons inside modal (find all with class and data-modal-id)
                modal.querySelectorAll('.close-modal').forEach(button => {
                     // Get the target modal ID from the button's data attribute
                     const targetModalId = button.dataset.modalId;
                     if (targetModalId) {
                        button.addEventListener('click', () => closeModal(targetModalId));
                     } else {
                         console.warn("Close button found without data-modal-id in modal:", modalId);
                     }
                });
                // Clicking outside modal content to close
                modal.addEventListener('click', (event) => {
                    // Close only if the click is directly on the modal backdrop (not content)
                    if (event.target === modal) {
                        closeModal(modalId);
                    }
                });
            } else {
                 console.warn("Modal element not found in DOM for ID:", modalId);
            }
        });

        // --- Add User Logic ---
        document.getElementById('addUserBtn')?.addEventListener('click', () => {
            openModal('addUserModal');
        });

        const addUserForm = document.getElementById('addUserForm');
        if (addUserForm) {
            addUserForm.addEventListener('submit', async (event) => {
                event.preventDefault();
                const modalId = 'addUserModal';
                displayModalError(modalId, ''); // Clear previous errors
                const saveButton = document.getElementById('saveAddUserButton');

                const password = document.getElementById('add_password').value;
                const confirmPassword = document.getElementById('add_confirm_password').value;

                if (password !== confirmPassword) {
                    displayModalError(modalId, 'Passwords do not match.'); return;
                }
                if (password.length < 8) {
                     displayModalError(modalId, 'Password must be at least 8 characters.'); return;
                }

                saveButton.disabled = true; saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';

                const formData = new FormData(addUserForm);
                const data = Object.fromEntries(formData.entries());

                const result = await performAjax('addUser', data);

                if (result.success) {
                    showNotification(result.message || 'User added successfully!', 'success');
                    closeModal(modalId);
                    // Consider updating table via JS instead of full reload for better UX
                    // For simplicity now, we reload:
                    window.location.reload();
                } else {
                    displayModalError(modalId, result.message || 'An unknown error occurred.');
                    saveButton.disabled = false; saveButton.innerHTML = '<i class="fas fa-save"></i> Add User';
                }
            });
        }

        // --- Edit User Logic ---
        // Add event listeners to edit buttons
        document.querySelectorAll('.edit-user-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const userId = button.getAttribute('data-user-id');
                if (userId) {
                    openEditUserModal(userId);
                }
            });
        });

        // Function to open and populate the edit user modal
        async function openEditUserModal(userId) {
            const modalId = 'editUserModal';
            openModal(modalId);

            const modal = modals[modalId];
            if (!modal) return;

            // Show loading indicator, hide form and error
            const loadingIndicator = modal.querySelector('#editUserLoadingIndicator');
            const errorIndicator = modal.querySelector('#editUserErrorIndicator');
            const userForm = modal.querySelector('#editUserForm');
            const saveButton = modal.querySelector('#saveEditUserButton');

            loadingIndicator.classList.add('active');
            errorIndicator.classList.remove('active');
            userForm.style.display = 'none';
            saveButton.disabled = true;

            // Fetch user data
            const result = await performAjax('getUser', { user_id: userId });

            loadingIndicator.classList.remove('active');

            if (result.success && result.data) {
                const userData = result.data;
                // Populate form
                modal.querySelector('#edit_user_id').value = userData.user_id || '';
                modal.querySelector('#edit_username').value = userData.username || '';
                modal.querySelector('#edit_full_name').value = userData.full_name || '';
                modal.querySelector('#edit_email').value = userData.email || '';
                modal.querySelector('#edit_reset_password').value = userData.reset_password || '';

                // Set select values
                const roleSelect = modal.querySelector('#edit_role');
                const statusSelect = modal.querySelector('#edit_status');
                const bureauSelect = modal.querySelector('#edit_bureau');

                if (roleSelect) {
                    for (let i = 0; i < roleSelect.options.length; i++) {
                        if (roleSelect.options[i].value === userData.role) {
                            roleSelect.selectedIndex = i;
                            break;
                        }
                    }
                }

                if (bureauSelect) {
                    for (let i = 0; i < bureauSelect.options.length; i++) {
                        if (bureauSelect.options[i].value === userData.bureau) {
                            bureauSelect.selectedIndex = i;
                            break;
                        }
                    }
                }

                if (statusSelect) {
                    for (let i = 0; i < statusSelect.options.length; i++) {
                        if (statusSelect.options[i].value === userData.status) {
                            statusSelect.selectedIndex = i;
                            break;
                        }
                    }
                }

                // Clear password fields
                modal.querySelector('#edit_password').value = '';
                modal.querySelector('#edit_confirm_password').value = '';
                resetPasswordStrength('edit_password_strength');

                userForm.style.display = 'grid';
                errorIndicator.classList.remove('active');
                saveButton.disabled = false;
            } else {
                errorIndicator.querySelector('span').textContent = result.message || 'Failed to load user data.';
                errorIndicator.classList.add('active');
                userForm.style.display = 'none';
                saveButton.disabled = true;
            }
        }

        // Handle edit user form submission
        const editUserForm = document.getElementById('editUserForm');
        if (editUserForm) {
            editUserForm.addEventListener('submit', async (event) => {
                event.preventDefault();
                const modalId = 'editUserModal';
                displayModalError(modalId, '');
                const saveButton = document.getElementById('saveEditUserButton');

                const password = document.getElementById('edit_password').value;
                const confirmPassword = document.getElementById('edit_confirm_password').value;

                // Validate password only if entered
                if (password || confirmPassword) {
                    if (password !== confirmPassword) {
                        displayModalError(modalId, 'Passwords do not match.');
                        return;
                    }
                    if (password.length < 8) {
                        displayModalError(modalId, 'Password must be at least 8 characters.');
                        return;
                    }
                }

                saveButton.disabled = true;
                saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

                const formData = new FormData(editUserForm);
                // Remove password fields if empty before sending
                if (!formData.get('password')) {
                    formData.delete('password');
                    formData.delete('confirm_password');
                }
                const data = Object.fromEntries(formData.entries());

                const result = await performAjax('updateUser', data);

                if (result.success) {
                    showNotification(result.message || 'User updated successfully!', 'success');
                    closeModal(modalId);

                    // Check if the updated user is the currently logged-in user
                    // If so, update the sidebar without reloading the page
                    const currentUserId = <?php echo isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'null'; ?>;
                    if (result.data && result.data.user_id == currentUserId) {
                        // Update sidebar user information
                        const usernameElement = document.querySelector('.app-title span');
                        const fullNameElement = document.querySelector('.app-title strong');

                        if (usernameElement && result.data.username) usernameElement.textContent = result.data.username;
                        if (fullNameElement && result.data.full_name) fullNameElement.textContent = result.data.full_name;

                        // Reload the page after a short delay to ensure other parts of the page are updated
                        setTimeout(() => {
                            window.location.reload();
                        }, 500);
                    } else {
                        // If not the current user, just reload the page
                        window.location.reload();
                    }
                } else {
                    displayModalError(modalId, result.message || 'An unknown error occurred.');
                    saveButton.disabled = false;
                    saveButton.innerHTML = '<i class="fas fa-save"></i> Save Changes';
                }
            });
        }

        // Add event listener for password field in edit form
        document.getElementById('edit_password')?.addEventListener('input', (e) =>
            displayPasswordStrength(e.target.value, 'edit_password_strength')
        );

        // --- Delete User Logic ---
        // Add event listeners to delete buttons
        document.querySelectorAll('.delete-user-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const userId = button.getAttribute('data-user-id');
                const username = button.getAttribute('data-username');
                if (userId && username) {
                    openDeleteUserModal(userId, username);
                }
            });
        });

        // Function to open the delete user modal
        function openDeleteUserModal(userId, username) {
            const modalId = 'deleteUserModal';
            openModal(modalId);

            const modal = modals[modalId];
            if (!modal) return;

            // Set the user ID and username
            document.getElementById('deleteUserName').textContent = username;
            const confirmButton = document.getElementById('confirmDeleteUserButton');

            // Update the confirm button to include the user ID
            confirmButton.setAttribute('data-user-id', userId);

            // Clear any previous error messages
            displayModalError(modalId, '');
        }

        // Handle delete user confirmation
        const confirmDeleteUserButton = document.getElementById('confirmDeleteUserButton');
        if (confirmDeleteUserButton) {
            confirmDeleteUserButton.addEventListener('click', async () => {
                const modalId = 'deleteUserModal';
                const userId = confirmDeleteUserButton.getAttribute('data-user-id');

                if (!userId) {
                    displayModalError(modalId, 'User ID not found.');
                    return;
                }

                confirmDeleteUserButton.disabled = true;
                confirmDeleteUserButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';

                const result = await performAjax('deleteUser', { user_id: userId });

                if (result.success) {
                    showNotification(result.message || 'User deleted successfully!', 'success');
                    closeModal(modalId);
                    window.location.reload();
                } else {
                    displayModalError(modalId, result.message || 'An unknown error occurred.');
                    confirmDeleteUserButton.disabled = false;
                    confirmDeleteUserButton.innerHTML = 'Delete';
                }
            });
        }




        // Settings popup and profile/logout functionality is now handled by settings.js


      }); // End DOMContentLoaded
    </script>
    <script src="js/settings.js"></script>

    <?php
      // Include common modals
      include 'modals.php';

      // Close DB connection if it's still open
      if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
          mysqli_close($conn);
      }
    ?>
</body>
</html>